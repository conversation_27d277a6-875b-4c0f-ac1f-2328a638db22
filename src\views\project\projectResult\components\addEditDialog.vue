<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body>

    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="small">

      <!-- 基本信息和项目里程碑并排显示 -->
      <el-row :gutter="20">
        <!-- 基本信息 -->
        <el-col :span="12">
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <el-row :gutter="2">
              <el-col :span="12">
                <el-form-item label="业务类型" prop="businessTypeId" required>
                  <el-select v-model="form.businessTypeId" placeholder="请选择" clearable filterable >
                    <el-option v-for="item in businessTypeOptions" :key="item.value" :value="item.value" :label="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="优先级" prop="priorityLevel" required>
                  <el-select v-model="form.priorityLevel" placeholder="请选择" clearable filterable >
                    <el-option v-for="item in dict.type.project_outcome_priority_level" :key="item.value" :value="item.value" :label="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="2">
              <el-col :span="12">
                <el-form-item label="负责项目经理" prop="projectManagers" required>
                  <el-select v-model="form.projectManagers" placeholder="请选择" multiple clearable filterable >
                    <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status" required>
                  <el-select v-model="form.status" placeholder="请选择" clearable filterable @change="handleStatusChange">
                    <el-option v-for="item in dict.type.project_outcome_status" :key="item.value" :value="item.value" :label="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="2">
              <el-col :span="12">
                <el-form-item label="成果类型" prop="resultType" required>
                  <el-select v-model="form.resultType" placeholder="请选择" clearable filterable >
                    <el-option v-for="item in dict.type.project_outcome_types" :key="item.value" :value="item.value" :label="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="完成时间" prop="completionTime">
                  <el-date-picker
                    v-model="form.completionTime"
                    type="date"
                    placeholder="请选择时间"
                    value-format="yyyy-MM-dd"
                    style="width: 100%; max-width: 180px"
                    :disabled="!isCompletedStatus">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item label="项目/任务名称" prop="projectTaskName" required>
                  <el-input v-model="form.projectTaskName" placeholder="请输入项目/任务名称" maxlength="30" show-word-limit style="width: 100%; max-width: 300px" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-col>

        <!-- 项目里程碑 -->
        <el-col :span="12">
          <div class="form-section">
            <div class="section-title">项目里程碑</div>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="完成评审" prop="milestoneRequirements">
                  <el-date-picker
                    v-model="form.milestoneRequirements"
                    type="date"
                    placeholder="请选择时间"
                    value-format="yyyy-MM-dd"
                    style="width: 180px">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="完成开发" prop="milestoneDevelopment">
                  <el-date-picker
                    v-model="form.milestoneDevelopment"
                    type="date"
                    placeholder="请选择时间"
                    value-format="yyyy-MM-dd"
                    style="width: 180px">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="完成测试验收" prop="milestoneTest">
                  <el-date-picker
                    v-model="form.milestoneTest"
                    type="date"
                    placeholder="请选择时间"
                    value-format="yyyy-MM-dd"
                    style="width: 180px">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="完成上线" prop="milestoneOnline">
                  <el-date-picker
                    v-model="form.milestoneOnline"
                    type="date"
                    placeholder="请选择时间"
                    value-format="yyyy-MM-dd"
                    style="width: 180px">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>

      <!-- 任务说明/进度、干系人、投入人力、工作量 -->
      <el-row :gutter="20">
        <!-- 任务说明/进度 -->
        <el-col :span="12">
          <div class="form-section">
            <div class="section-title">任务说明/进度</div>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="需求评审" prop="requirementsProgress">
                  <el-input-number
                    v-model="form.requirementsProgress"
                    :min="0"
                    :max="100"
                    style="width: 200px"
                    controls-position="right">
                  </el-input-number>
                  <span style="margin-left: 8px;">%</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="开发进度" prop="developmentProgress">
                  <el-input-number
                    v-model="form.developmentProgress"
                    :min="0"
                    :max="100"
                    style="width: 200px"
                    controls-position="right">
                  </el-input-number>
                  <span style="margin-left: 8px;">%</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="测试验收进度" prop="testProgress">
                  <el-input-number
                    v-model="form.testProgress"
                    :min="0"
                    :max="100"
                    style="width: 200px"
                    controls-position="right">
                  </el-input-number>
                  <span style="margin-left: 8px;">%</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-col>

        <!-- 干系人 -->
        <el-col :span="12">
          <div class="form-section">
            <div class="section-title">干系人</div>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="开发" prop="devTeams">
                  <el-select v-model="form.devTeams" placeholder="请选择" multiple clearable filterable style="width: 250px">
                    <el-option v-for="item in dict.type.project_outcome_dev_dept" :key="item.value" :value="item.value" :label="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="测试" prop="testTeams">
                  <el-select v-model="form.testTeams" placeholder="请选择" multiple clearable filterable style="width: 250px">
                    <el-option v-for="item in dict.type.project_outcome_test_dept" :key="item.value" :value="item.value" :label="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="产品" prop="productManagers">
                  <el-input v-model="form.productManagers" placeholder="可填多个，用“、”隔开" style="width: 250px" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>

      <!-- 投入人力和工作量 -->
      <el-row :gutter="20">
        <!-- 投入人力 -->
        <el-col :span="12">
          <div class="form-section">
            <div class="section-title">投入人力</div>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="开发" prop="devManpower">
                  <el-input-number
                    v-model="form.devManpower"
                    :min="0"
                    style="width: 200px"
                    controls-position="right">
                  </el-input-number>
                  <span style="margin-left: 8px;">人</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="测试" prop="testManpower">
                  <el-input-number
                    v-model="form.testManpower"
                    :min="0"
                    style="width: 200px"
                    controls-position="right">
                  </el-input-number>
                  <span style="margin-left: 8px;">人</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-col>

        <!-- 工作量 -->
        <el-col :span="12">
          <div class="form-section">
            <div class="section-title">工作量</div>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="开发" prop="devWorkload">
                  <el-input-number
                    v-model="form.devWorkload"
                    :min="0"
                    style="width: 200px"
                    controls-position="right">
                  </el-input-number>
                  <span style="margin-left: 8px;">人日</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="15">
              <el-col :span="24">
                <el-form-item label="测试" prop="testWorkload">
                  <el-input-number
                    v-model="form.testWorkload"
                    :min="0"
                    style="width: 200px"
                    controls-position="right">
                  </el-input-number>
                  <span style="margin-left: 8px;">人日</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>

      <!-- 需求背景 -->
      <div class="form-section">
        <div class="section-title">需求背景</div>
        <el-row>
          <el-col :span="24">
            <el-form-item prop="requirementBackground">
              <el-input
                v-model="form.requirementBackground"
                type="textarea"
                :rows="4"
                placeholder="请输入需求背景"
                style="width: 100%; max-width: 1100px;" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getBusinessTypeOptions, projectResultAdd, projectResultEdit } from "@/api/project/projectResult"

export default {
  name: "AddEditDialog",
  dicts: [
    'project_outcome_types',
    'project_outcome_status',
    'project_outcome_priority_level',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager',
    'project_outcome_dev_dept',
    'project_outcome_test_dept'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '新增'
    },
    dialogData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    // 项目/任务名称长度验证
    const validateProjectTaskName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入项目/任务名称'))
      } else if (value.length > 30) {
        callback(new Error('项目/任务名称不能超过30个字符'))
      } else {
        callback()
      }
    }

    // 进度值验证
    const validateProgress = (rule, value, callback) => {
      if (value !== null && value !== undefined && (value < 0 || value > 100)) {
        callback(new Error('进度值必须在0-100之间'))
      } else {
        callback()
      }
    }

    // 人力和工作量验证
    const validatePositiveNumber = (rule, value, callback) => {
      if (value !== null && value !== undefined && value < 0) {
        callback(new Error('数值不能为负数'))
      } else {
        callback()
      }
    }

    return {
      visible: false,
      form: {
        id: null, // 主键ID，编辑时使用
        projectTaskName: '',
        businessTypeId: '',
        resultType: '',
        status: '',
        priorityLevel: '',
        requirementBackground: '',
        projectManagers: [],
        completionTime: '',
        // 项目里程碑
        milestoneRequirements: '',
        milestoneDevelopment: '',
        milestoneTest: '',
        milestoneOnline: '',
        // 任务说明/进度
        requirementsProgress: null,
        developmentProgress: null,
        testProgress: null,
        // 干系人
        devTeams: [],
        testTeams: [],
        productManagers: '',
        // 投入人力
        devManpower: null,
        testManpower: null,
        // 工作量
        devWorkload: null,
        testWorkload: null
      },
      rules: {
        projectTaskName: [
          { required: true, validator: validateProjectTaskName, trigger: 'blur' }
        ],
        businessTypeId: [
          { required: true, message: '请选择业务类型', trigger: 'change' }
        ],
        resultType: [
          { required: true, message: '请选择成果类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        priorityLevel: [
          { required: true, message: '请选择优先级', trigger: 'change' }
        ],
        projectManagers: [
          { required: true, message: '请选择负责项目经理', trigger: 'change' }
        ],
        requirementsProgress: [
          { validator: validateProgress, trigger: 'blur' }
        ],
        developmentProgress: [
          { validator: validateProgress, trigger: 'blur' }
        ],
        testProgress: [
          { validator: validateProgress, trigger: 'blur' }
        ],
        devManpower: [
          { validator: validatePositiveNumber, trigger: 'blur' }
        ],
        testManpower: [
          { validator: validatePositiveNumber, trigger: 'blur' }
        ],
        devWorkload: [
          { validator: validatePositiveNumber, trigger: 'blur' }
        ],
        testWorkload: [
          { validator: validatePositiveNumber, trigger: 'blur' }
        ]
      },
      // 业务类型选项
      businessTypeOptions: []
    }
  },
  computed: {
    // 判断是否为已完成状态
    isCompletedStatus() {
      // 假设已完成状态的值为 '3'，需要根据实际字典值调整
      return this.form.status === '3'
    }
  },
  watch: {
    dialogVisible(val) {
      this.visible = val
      if (val) {
        this.initForm()
        this.loadOptions()
      }
    },
    visible(val) {
      this.$emit('update:dialogVisible', val)
    },
    dialogData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initForm()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /** 初始化表单 */
    initForm() {
      if (this.dialogData && Object.keys(this.dialogData).length > 0) {
        this.form = {
          ...this.form,
          ...this.dialogData,
          // 处理多选字段
          projectManagers: this.dialogData.projectManagers ? this.dialogData.projectManagers.split(',') : [],
          devTeams: this.dialogData.devTeams ? this.dialogData.devTeams.split(',') : [],
          testTeams: this.dialogData.testTeams ? this.dialogData.testTeams.split(',') : []
        }
      } else {
        this.resetForm()
      }
    },

    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null, // 主键ID，编辑时使用
        projectTaskName: '',
        businessTypeId: '',
        resultType: '',
        status: '',
        priorityLevel: '',
        requirementBackground: '',
        projectManagers: [],
        completionTime: '',
        // 项目里程碑
        milestoneRequirements: '',
        milestoneDevelopment: '',
        milestoneTest: '',
        milestoneOnline: '',
        // 任务说明/进度
        requirementsProgress: null,
        developmentProgress: null,
        testProgress: null,
        // 干系人
        devTeams: [],
        testTeams: [],
        productManagers: '',
        // 投入人力
        devManpower: null,
        testManpower: null,
        // 工作量
        devWorkload: null,
        testWorkload: null
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },

    /** 状态变化处理 */
    handleStatusChange(value) {
      // 如果状态不是已完成，清空完成时间
      if (value !== '3') {
        this.form.completionTime = ''
      }
    },

    /** 加载下拉选项数据 */
    loadOptions() {
      // 加载业务类型选项
      getBusinessTypeOptions().then(res => {
        if (res.code === 200 && res.rows) {
          this.businessTypeOptions = res.rows.map(item => ({
            value: item.id,
            label: item.businessTypeName
          }))
        }
      }).catch(() => {
        console.warn('获取业务类型选项失败')
      })
    },

    /** 提交表单 */
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 处理提交数据
          const submitData = {
            ...this.form,
            // 将多选数组转换为逗号分隔的字符串
            projectManagers: Array.isArray(this.form.projectManagers) ? this.form.projectManagers.join(',') : this.form.projectManagers,
            devTeams: Array.isArray(this.form.devTeams) ? this.form.devTeams.join(',') : this.form.devTeams,
            testTeams: Array.isArray(this.form.testTeams) ? this.form.testTeams.join(',') : this.form.testTeams,
            // 确保数字类型字段正确处理
            businessTypeId: this.form.businessTypeId ? Number(this.form.businessTypeId) : null,
            // 处理日期字段，转换为后端期望的格式
            completionTime: this.formatDateForSubmit(this.form.completionTime),
            milestoneRequirements: this.formatDateForSubmit(this.form.milestoneRequirements),
            milestoneDevelopment: this.formatDateForSubmit(this.form.milestoneDevelopment),
            milestoneTest: this.formatDateForSubmit(this.form.milestoneTest),
            milestoneOnline: this.formatDateForSubmit(this.form.milestoneOnline)
          }

          // 移除空值字段，避免传递undefined
          Object.keys(submitData).forEach(key => {
            if (submitData[key] === '' || submitData[key] === undefined) {
              submitData[key] = null
            }
          })

          // 判断是新增还是编辑
          const isEdit = submitData.id && submitData.id !== ''
          const apiCall = isEdit ? projectResultEdit : projectResultAdd
          const actionText = isEdit ? '修改' : '新增'

          // 显示加载状态
          const loading = this.$loading({
            lock: true,
            text: `${actionText}中...`,
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          // 调用API
          apiCall(submitData).then(res => {
            loading.close()
            if (res.code === 200) {
              this.$message.success(`${actionText}成功`)
              this.cancel()
              this.$emit('callback')
            } else {
              this.$message.error(res.msg || `${actionText}失败`)
            }
          }).catch(error => {
            loading.close()
            console.error(`${actionText}失败:`, error)
            this.$message.error(`${actionText}失败`)
          })
        } else {
          this.$message.error('请完善必填信息')
          return false
        }
      })
    },

    /** 取消 */
    cancel() {
      this.visible = false
      this.resetForm()
    },

    /** 格式化日期用于提交 */
    formatDateForSubmit(dateStr) {
      if (!dateStr) return null

      // 如果已经是完整的日期时间格式，直接返回
      if (dateStr.includes(' ')) {
        return dateStr
      }

      // 如果只是日期格式（yyyy-MM-dd），添加时间部分
      if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return `${dateStr} 00:00:00`
      }

      return dateStr
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.form-section {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
  height: auto;
  min-height: 200px;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.el-form-item {
  margin-bottom: 15px;
}

.el-form-item__label {
  font-weight: 500;
  font-size: 13px;
  line-height: 32px;
}

/* 必填项标识 */
.el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 输入框样式调整 */
.el-input-number {
  width: 150px;
}

.el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-select .el-input__inner {
  height: 32px;
  line-height: 32px;
}

/* 多选框样式 */
.el-select .el-tag {
  margin-right: 6px;
  height: 24px;
  line-height: 22px;
}

/* 文本域样式 */
.el-textarea__inner {
  resize: vertical;
  min-height: 80px;
}

/* 日期选择器样式 */
.el-date-editor .el-input__inner {
  height: 32px;
  line-height: 32px;
}

/* 数字输入框后面的单位样式 */
.el-form-item__content span {
  color: #606266;
  font-size: 13px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0;
}

/* 调整列间距 */
.el-col {
  margin-bottom: 0;
}
</style>
