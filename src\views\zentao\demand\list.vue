<template>
<div class="app-container">
  <div class="table">
    <div style="padding-top: 20px; padding-left: 10px">
          <el-form :inline="true"  @submit.native.prevent ref="" :model="queryForm">
            <el-form-item label="所属产品：" >
              <el-select
                clearable
                placeholder="请选择"
                v-model="queryForm.productId"
              >
                <el-option value="" label="全部"></el-option>
                <el-option
                  v-for="item in productData"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="当前状态：" >
              <el-select
                clearable
                placeholder="请选择"
                v-model="queryForm.status"
              >
                <el-option
                  v-for="item in optionsData"
                  :key="item.value"
                  :value="item.value"
                  :label="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <span>时间&nbsp;&nbsp;&nbsp;</span>
              <el-date-picker v-model="queryForm.beginDate" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 00:00:00"></el-date-picker>
              <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <el-date-picker v-model="queryForm.endDate" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 23:59:59"></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                type="primary"
                @click="handleQuery"
              >
                查询
              </el-button>
              <el-button
                icon="el-icon-refresh"
                type="primary"
                @click="resetQuery"
              >
              </el-button>
            </el-form-item>
            <el-form-item label="提示：双击某行可打开禅道的对应明细页面">
            </el-form-item>
          </el-form>
    </div>

    <el-table
      v-loading="listLoading"
      border
      stripe
      :data="tableData"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="onSortChange"
      @row-dblclick="handleTableRow"
      height="calc(100vh - 230px)"
      ref="tableList"
    >
     <el-table-column
            type="selection"
            width="55">
      </el-table-column>
      <el-table-column label="所属产品" width="220" prop="productName" sortable>
        <template slot-scope="scope">
          {{scope.row.productName}}
        </template>
      </el-table-column>
      <el-table-column label="需求标题" width="200" prop="title" sortable>
        <template slot-scope="scope">
          <div v-html="(scope.row.title)"></div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="当前状态" width="100" sortable>
        <template slot-scope="scope">
          {{scope.row.status}}
        </template>
      </el-table-column>
      <el-table-column label="所处阶段" width="100" prop="stage" sortable>
        <template slot-scope="scope">
          {{scope.row.stage}}
        </template>
      </el-table-column>
      <el-table-column label="创建人" width="100" prop="openedBy" sortable >
        <template slot-scope="scope">
          {{scope.row.openedBy}}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="100" prop="openedDate" sortable >
        <template slot-scope="scope">
          {{scope.row.openedDate}}
        </template>
      </el-table-column>
      <el-table-column label="跟进人" width="100" prop="assignedto" sortable >
        <template slot-scope="scope">
          {{scope.row.assignedto}}
        </template>
      </el-table-column>
      <el-table-column label="跟进时间" width="100" prop="assigneddate" sortable >
        <template slot-scope="scope">
          {{scope.row.assigneddate}}
        </template>
      </el-table-column>
      <el-table-column label="评审人" width="100" prop="reviewer" sortable >
        <template slot-scope="scope">
          {{scope.row.reviewer}}
        </template>
      </el-table-column>
      <el-table-column label="评审时间" width="100" prop="revieweddate" sortable >
        <template slot-scope="scope">
          {{scope.row.revieweddate}}
        </template>
      </el-table-column>
      <el-table-column label="关闭人" width="100" prop="closedby" sortable >
        <template slot-scope="scope">
          {{scope.row.closedby}}
        </template>
      </el-table-column>
      <el-table-column label="关闭时间" width="100" prop="closeddate" sortable >
        <template slot-scope="scope">
          {{scope.row.closeddate}}
        </template>
      </el-table-column>
      <el-table-column label="关闭原因" width="100" prop="closedreason" sortable >
        <template slot-scope="scope">
          {{scope.row.closedreason}}
        </template>
      </el-table-column>
      <el-table-column label="用例总量" width="100" prop="caseCount" sortable >
      </el-table-column>
      <el-table-column label="bug总量" width="100" prop="bugCount" sortable >
      </el-table-column>
      <el-table-column label="任务数" width="100" prop="taskCount" sortable >
      </el-table-column>
      <el-table-column label="发布数" width="100" prop="releaseCount" sortable >
      </el-table-column>


    </el-table>

    <!-- <el-pagination
      :current-page="1"
      :layout="layout"
      :page-size="50"
      :total="100"
      background
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    /> -->
  </div>
  <div class="container">
    <el-pagination
      class="rear-page"
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      layout="prev, pager, next, slot, jumper, sizes, total"
      :total="queryForm.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- slot部分，跳转末页 -->
      <button
        class="lastPage"
        :disabled="queryForm.lastPageDisabled"
        @click="toLastPage"
      >
        <i class="el-icon-d-arrow-right"></i>
      </button>
    </el-pagination>
    <el-pagination
      class="ahead-page"
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      layout="slot"
      :total="queryForm.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- slot部分，跳转首页 -->
      <button
        class="firstPage"
        :disabled="queryForm.firstPageDisabled"
        @click="toFirstPage"
      >
        <i class="el-icon-d-arrow-left"></i>
      </button>
    </el-pagination>
     </div>
 </div>
</template>

<script>
  import { getDemandPage } from '@/api/demandQuery'
  import { productList } from '@/api/commonBiz'
  export default {
    name: 'Demand',
    data(){
      return{
        layout: 'total, sizes, prev, pager, next, jumper',
        tableData: [], // 表格数据列表
        listLoading: false,
        queryForm : {
            productId: '',
            status: 'all',
            beginDate: '',
            endDate: '',
            pageSize: 50, //  展示数量
            total: 0, //  总数
            firstPageDisabled: false, //  首页
            lastPageDisabled: true, //  末页
        },
        productData: [],
        optionsData: [{
            value: 'all',
            name: '全部'
            }, {
            value: 'draft',
            name: '草稿'
            }, {
            value: 'active',
            name: '活跃'
            }, {
            value: 'changed',
            name: '变更'
            }, {
            value: 'closed',
            name: '关闭'
            }],
        };
    },
    watch: {
      // 分页 计算首页和末页
      queryForm: {
        handler(newVal) {
          let pages = Math.ceil(newVal.total / newVal.pageSize);
          if (pages === 0) {
            // 数据(totalResult)为0
            this.queryForm.firstPageDisabled = true; // 首页按钮是否禁用
            this.queryForm.lastPageDisabled = true; // 末页按钮是否禁用
          } else {
            this.queryForm.firstPageDisabled = newVal.pageNo === 1;
            this.queryForm.lastPageDisabled = newVal.pageNo === pages;
          }
        },
        // 一进页面就执行
        immediate: true,
        deep: true,
      },
      '$route.query': {
        handler(newVal) {
          // 检查是否路由到当前组件
        if (this.$route.name !== 'Demand') {
            return;  // 如果不是需求列表组件则直接返回
        }
          if (this.$route.query.params) {
            console.log('路由参数', this.$route.query.params)
            let params = JSON.parse(this.$route.query.params)
            this.queryForm.productId = Number(params.productId)
            this.queryForm.status = params.status
            setTimeout(() => {
              this.queryForm.beginDate = params.beginDate
              this.queryForm.endDate = params.endDate
              this.handleQuery()
            }, 1000)
            const newQuery = { ...this.$route.query }
            delete newQuery.params
            // 将剩余的查询参数设置回路由
            this.$router.replace({ query: newQuery })
          }
        },
        // 一进页面就执行
        immediate: true,
        deep: true,
      }
    },
    methods:{
      // 点击排序
      onSortChange(column) {
        if (column.prop === 'caseCount' || column.prop === 'bugCount') {
          this.queryForm.orderByField = column.prop;
          this.queryForm.orderRule = column.order === "ascending" ? 'asc' : 'desc';
          // 获取后台列表数据
          this.getList()
        }
      },
      //   改变页码
      handlePageChange({ pageNo, pageSize }) {
        this.queryForm.pageNo = pageNo;
        this.queryForm.pageSize = pageSize;
      },

      //   改变每页显示数量
      handleSizeChange(pageSize) {
        this.queryForm.pageSize = pageSize;
      },

      //   改变当前页码
      handleCurrentChange(pageNo) {
        this.queryForm.pageNo = pageNo;
      },

      //   前往首页
      toFirstPage() {
        this.handleCurrentChange(1);
      },

      //  前往末页
      toLastPage() {
        let max = Math.ceil(this.queryForm.total / this.queryForm.pageSize);
        this.handleCurrentChange(max);
      },

      // 查询
      handleQuery(){
          this.getList();
      },

      // 重置
      resetQuery(){
        this.queryForm = {
            productId: '',
            status: 'all',
            beginDate: '',
            endDate: '',
            pageSize: 50, //  展示数量
            total: 0, //  总数
            firstPageDisabled: false, //  首页
            lastPageDisabled: true, //  末页
        }
        this.$refs.tableList.clearSort();
        this.initTime()
        this.getList()
      },

      // 当前页面
      handleCurrentChange(val) {
          this.queryForm.pageNo = val
          this.getList()
        },

      // 页数
      handleSizeChange(val) {
          this.queryForm.pageSize = val
          this.getList()
        },

      // 下拉框选择
      handleSelectionChange(val){

      },

      // 初始化
      async getList(){
          this.listLoading = true
          this.tableData = []
          const resp = await getDemandPage(this.queryForm)
          this.formatResource(resp.data.records)
          this.listLoading = false
          this.queryForm.total = resp.data.total * 1
          // this.queryForm.pageSizes = resp.data.data.pages * 1

          // this.queryForm.pageSize = resp.data.data.pageSize
      },

      formatResource(data){
          if (data) {
            data.forEach((item) => {
              let resource = {
                id: item.id,
                productName: item.productName,
                title: item.title,
                status: item.status,
                stage: item.stage,
                openedBy: item.openedby,
                openedDate: item.openeddate,
                assignedto: item.assignedto,
                assigneddate: item.assigneddate,
                reviewer: item.reviewer,
                revieweddate: item.revieweddate,
                lasteditedBy: item.lasteditedBy,
                lastediteddate: item.lastediteddate,
                closedby: item.closedby,
                closeddate: item.closeddate,
                closedreason: item.closedreason,
                duplicatestory: item.duplicatestory,
                tobug: item.tobug,
                bugCount: item.bugCount,
                caseCount: item.caseCount,
                taskCount: item.taskCount,
                releaseCount: item.releaseCount
              }
              this.tableData.push(resource)
            })
          }
      },
      // 处理时间
      getFormatDate(date) {
        var month = date.getMonth() + 1
        var strDate = date.getDate()
        if (month >= 1 && month <= 9) {
          month = '0' + month
        }
        if (strDate >= 0 && strDate <= 9) {
          strDate = '0' + strDate
        }
        var currentDate = date.getFullYear() + '-' + month + '-' + strDate + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
        return currentDate
      },
      // 初始化时间
      initTime() {
        const endDate = this.getFormatDate(new Date()).substr(0, 11) + '23:59:59'
        const beginDate = this.getFormatDate(new Date(new Date() - 3600 * 1000 * 24 * 29)).substr(0, 11) + '00:00:00'
        this.queryForm.beginDate = beginDate
        this.queryForm.endDate = endDate
      },
      // 双击table表行
      handleTableRow(row, event, column) {
          const url = 'http://pm.qmqb.top/story-view-'+ row.id  +'.html/'
          window.open(url, "_blank");
      },
      // 获取产品下拉框列表
      getProductList () {
        productList().then(res => {
          if (res.code === 200) {
            this.productData = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    created(){
      this.getProductList()
      this.initTime()
    },
    mounted(){

    }
}
</script>

<style lang=scss scoped>
.app-container {
  padding: 20px;
}
.table {
    background-color: #fff;
    .tiltle-cn {
      color: #2184d8;
    }
    .control-cell {
      display: flex;
      flex-direction: row;
      .control-cell-item {
        margin-left: 10px;
      }
    }
  }

 .container{
    float: left;
 }
  .el-pagination {
  float: right;
  margin-top: 10px;
}
.el-pagination.ahead-page {
  padding-right: 0px;
}
.el-pagination.rear-page {
  padding-left: 0px;
}
.firstPage,
.lastPage {
  background-color: white;
  cursor: pointer;
}

</style>
