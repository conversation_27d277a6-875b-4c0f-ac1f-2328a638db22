import request from '@/utils/request'

// 查询项目成果列表
export function projectResultList(query) {
  return request({
    url: '/system/projectResult/list',
    method: 'get',
    params: query
  })
}

// 查询项目成果详细
export function projectResultDetail(id) {
  return request({
    url: '/system/projectResult/' + id,
    method: 'get'
  })
}

// 新增项目成果
export function projectResultAdd(data) {
  return request({
    url: '/system/projectResult',
    method: 'post',
    data: data
  })
}

// 修改项目成果
export function projectResultEdit(data) {
  return request({
    url: '/system/projectResult',
    method: 'put',
    data: data
  })
}

// 删除项目成果
export function projectResultDelete(id) {
  return request({
    url: '/system/projectResult/' + id,
    method: 'delete'
  })
}

// 批量删除项目成果
export function projectResultBatchDelete(ids) {
  return request({
    url: '/system/projectResult/' + ids,
    method: 'delete'
  })
}

// 同步项目数据
export function projectResultSync(data) {
  return request({
    url: '/system/projectResult/sync',
    method: 'post',
    data: data
  })
}

// 归档项目成果
export function projectResultArchive(id) {
  return request({
    url: '/system/projectResult/archive/' + id,
    method: 'put'
  })
}

// 导出项目成果
export function projectResultExport(query) {
  return request({
    url: '/system/projectResult/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取业务类型选项
export function getBusinessTypeOptions() {
  return request({
    url: '/system/businessType/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 1000  // 获取所有业务类型
    }
  })
}

// 获取成果类型选项
export function getResultTypeOptions() {
  return request({
    url: '/project/projectResult/resultTypes',
    method: 'get'
  })
}

// 获取项目经理选项
export function getProjectManagerOptions() {
  return request({
    url: '/project/projectResult/projectManagers',
    method: 'get'
  })
}

// 获取业务大类选项
export function getBusinessCategoryMajorOptions() {
  return request({
    url: '/project/projectResult/businessCategoryMajor',
    method: 'get'
  })
}

// 获取业务小类选项
export function getBusinessCategoryMinorOptions(majorId) {
  return request({
    url: '/project/projectResult/businessCategoryMinor',
    method: 'get',
    params: { majorId }
  })
}

// 业务类型管理相关接口
export function businessTypeList(query) {
  return request({
    url: '/project/businessType/list',
    method: 'get',
    params: query
  })
}

export function businessTypeAdd(data) {
  return request({
    url: '/project/businessType',
    method: 'post',
    data: data
  })
}

export function businessTypeEdit(data) {
  return request({
    url: '/project/businessType',
    method: 'put',
    data: data
  })
}

export function businessTypeDelete(id) {
  return request({
    url: '/project/businessType/' + id,
    method: 'delete'
  })
}
